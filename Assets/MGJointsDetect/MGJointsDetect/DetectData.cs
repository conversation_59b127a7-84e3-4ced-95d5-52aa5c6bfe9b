using System;
using System.Collections.Generic;

namespace MGJointsDetect
{
    [Serializable]
    public class Param<T>
    {
        public int msgID = 0;   // required: 消息id
        public T args;          // required: 参数
    }
    
    [Serializable]
    public class DriveData
    {
        // public int width = 1280;
        // public int height = 720;
        public JointsDriveData jointsDriveData = null;
    }

    [Serializable]
    public class JointsDriveData
    {
        public float timeStamp = -1; // s
        public List<List<float>> eulers;
        public List<List<float>> left_hand;
        public List<List<float>> right_hand;

        public List<float> bodyRoot;
        public List<int> foot_up;

        public List<List<float>> body_uvd;
        public int width;
        public int height;


        [Obsolete("废弃,请使用bodyRoot")]
        public List<List<float>> root;
        [Obsolete("废弃,请使用eulers")]
        public List<List<float>> body_oula;
        [Obsolete("废弃,请使用left_hand")]
        public List<List<float>> left_hand_oula;
        [Obsolete("废弃,请使用right_hand")]
        public List<List<float>> right_hand_oula;

        public void Check()
        {
            if (root != null && root.Count>0)
            {
                bodyRoot = root[0];
            }
            if (body_oula != null && body_oula.Count>0)
            {
                eulers = body_oula;
            }
            if (left_hand_oula != null && left_hand_oula.Count>0)
            {
                left_hand = left_hand_oula;
            }
            if (right_hand_oula != null && right_hand_oula.Count>0)
            {
                right_hand = right_hand_oula;
            }
        }
    }

    public class DetectResult
    {
        public HashSet<DetectType> lastTypes = new HashSet<DetectType>();
        public HashSet<DetectType> currentTypes = new HashSet<DetectType>();

        //拳击游戏
        public List<BoxingDectectModel> currentBoxingModels = new List<BoxingDectectModel>();
        //public BoxingDectectModel currentBoxingModel = new BoxingDectectModel();

        //网球游戏
        public List<TennisDectectModel> currentTennisModels = new List<TennisDectectModel>();
        
        public List<DanceDectectModel> currentDanceModels = new List<DanceDectectModel>();
        
        public void ResetCurrent()
        {
            lastTypes = currentTypes;
            currentTypes = new HashSet<DetectType>();
        }
    }

    public class BoxingDectectModel
    {
        //姿态类型
        /*
            pBoxingIdle：idle状态。此状态双手有出拳动作和无动作；
            pAllDefend ：双手防御，此状态无出拳动作；
            pLeftDefend：左臂防御，此状态右手有出拳动作和无动作。
            pRightDefend：右臂防御，此状态左手有出拳动作和无动作。
         */
        public DetectType dectectPose = DetectType.pBoxingIdle;

        //动作类型：左右直拳，左右勾拳，左右摆拳， 无动作等
        public DetectType dectectAction = DetectType.None;

        //检测到动作使用的帧数
        public int dectectActionSpeed;
    }
    
    public class TennisDectectModel
    {
        //动作类型：左右直拳，左右勾拳，左右摆拳， 无动作等
        public DetectType dectectAction = DetectType.None;
        
        //检测到动作使用的帧数
        public int dectectActionSpeed;
    }

    public class DanceDectectModel
    {
        /*
         * 0 1 2
         * 3 4 5
         * 6 7 8
         */
        //左手所在区域
        public int leftHandArea;
        //右手所在区域
        public int rightHandArea;
        //左手到左肩距离
        public float leftHandDistance;
        //右手到右肩距离
        public float rightHandDistance;

    }
}