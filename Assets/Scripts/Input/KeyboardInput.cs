using System;
using UnityEngine;
using MGJointsDetect;

public class KeyboardInput : MonoBehaviour
{

    public KeyEvent<DetectType>[] ActionDetectedEventBindings =
    {
        new(KeyCode.S, DetectType.aTennisThrow),
        new(KeyCode.W, DetectType.aTennisFirstHit),
        new(KeyCode.Z, DetectType.aTennisPickUpL),
        new(KeyCode.X, DetectType.aTennisPickUpR),
        new(KeyCode.A, DetectType.aTennisFlatL),
        new(KeyCode.D, DetectType.aTennisFlatR),
        new(KeyCode.Q, DetectType.aTennisSmashL),
        new(KeyCode.E, DetectType.aTennisSmashR),
    };
    
    
    public KeyEvent<DanceDectectModel>[] _ActionDetectedEventBindings =
    {
        new(KeyCode.Q, new DanceDectectModel(){leftHandArea = 0,rightHandArea = 0}),
        new(KeyCode.W, new DanceDectectModel(){leftHandArea = 1,rightHandArea = 1}),
        new(KeyCode.E, new DanceDectectModel(){leftHandArea = 2,rightHandArea = 2}),
        new(KeyCode.A, new DanceDectectModel(){leftHandArea = 3,rightHandArea = 3}),
        new(KeyCode.S, new DanceDectectModel(){leftHandArea = 4,rightHandArea = 4}),
        new(KeyCode.D, new DanceDectectModel(){leftHandArea = 5,rightHandArea = 5}),
        new(KeyCode.Z, new DanceDectectModel(){leftHandArea = 6,rightHandArea = 6}),
        new(KeyCode.X, new DanceDectectModel(){leftHandArea = 7,rightHandArea = 7}),
        new(KeyCode.C, new DanceDectectModel(){leftHandArea = 8,rightHandArea = 8}),
    };

    private bool lastMoveCheck = false;

    private DetectType currentPose = DetectType.pBoxingIdle;

    private void Update()
    {
        foreach (var item in _ActionDetectedEventBindings)
        {
            if (Input.GetKeyDown(item.key))
            {
                EventManager.TriggerEvent(EventName.PoseDetectedEvent, DetectType.pBoxingIdle);
                EventManager.TriggerEvent(EventName.ActionDetectedEvent, item.eventParam);
            }
        }
    }
}

[Serializable]
public struct KeyEvent<T>
{
    public KeyCode key;
    public T eventParam;

    public KeyEvent(KeyCode key, T eventParam)
    {
        this.key = key;
        this.eventParam = eventParam;
    }
}