using UnityEngine;

/// <summary>
/// 舞蹈系统设置助手
/// 用于在Unity编辑器中快速设置整个舞蹈游戏系统
/// </summary>
public class DanceSystemSetup : MonoBehaviour
{
    [Header("系统组件")]
    [SerializeField] private DanceGameController gameController;
    [SerializeField] private DancePromptManager promptManager;
    [SerializeField] private DanceGameUI gameUI;
    
    [Header("预制体设置")]
    [SerializeField] private GameObject promptPointPrefab;
    
    [Header("玩家设置")]
    [SerializeField] private Transform playerTransform;
    [SerializeField] private string playerPath = "Players/SK_GamerGirl_03 Pink Variant";
    
    [Header("自动设置选项")]
    [SerializeField] private bool autoFindPlayer = true;
    [SerializeField] private bool createDefaultPrefab = true;
    [SerializeField] private bool setupUI = true;
    
    private void Awake()
    {
        if (autoFindPlayer)
        {
            FindPlayerTransform();
        }
    }
    
    /// <summary>
    /// 查找玩家Transform
    /// </summary>
    private void FindPlayerTransform()
    {
        // if (playerTransform != null) return;
        //
        // 尝试通过路径查找
        GameObject playerGO = GameObject.Find(playerPath);
        if (playerGO != null)
        {
            playerTransform = playerGO.transform;
            Debug.Log($"找到玩家: {playerTransform.name}");
            SetupCompleteSystem();
            return;
        }
        
        // 尝试通过Players节点查找
        GameObject playersGO = GameObject.Find("Players");
        if (playersGO != null && playersGO.transform.childCount > 1)
        {
            playerTransform = playersGO.transform.GetChild(1); // 第二个子对象是玩家
            Debug.Log($"通过Players节点找到玩家: {playerTransform.name}");
            return;
        }
        
        Debug.LogWarning("未找到玩家Transform，请手动设置");
    }
    
    /// <summary>
    /// 完整设置舞蹈系统
    /// </summary>
    [ContextMenu("完整设置舞蹈系统")]
    public void SetupCompleteSystem()
    {
        Debug.Log("开始设置舞蹈系统...");
        
        // 1. 查找或创建游戏控制器
        SetupGameController();
        
        // 2. 设置提示点管理器
        SetupPromptManager();
        
        // 3. 设置UI
        if (setupUI)
        {
            SetupGameUI();
        }
        
        // 4. 创建默认预制体
        if (createDefaultPrefab)
        {
            CreateDefaultPromptPrefab();
        }
        
        // 5. 连接所有组件
        ConnectComponents();
        
        Debug.Log("舞蹈系统设置完成！");
    }
    
    /// <summary>
    /// 设置游戏控制器
    /// </summary>
    private void SetupGameController()
    {
        if (gameController == null)
        {
            gameController = FindObjectOfType<DanceGameController>();
            if (gameController == null)
            {
                GameObject controllerGO = new GameObject("DanceGameController");
                controllerGO.transform.SetParent(transform);
                gameController = controllerGO.AddComponent<DanceGameController>();
                Debug.Log("创建了DanceGameController");
            }
        }
    }
    
    /// <summary>
    /// 设置提示点管理器
    /// </summary>
    private void SetupPromptManager()
    {
        if (promptManager == null)
        {
            promptManager = FindObjectOfType<DancePromptManager>();
            if (promptManager == null)
            {
                GameObject managerGO = new GameObject("DancePromptManager");
                managerGO.transform.SetParent(transform);
                promptManager = managerGO.AddComponent<DancePromptManager>();
                Debug.Log("创建了DancePromptManager");
            }
        }
        
        // 设置玩家引用
        if (playerTransform != null)
        {
            var playerField = typeof(DancePromptManager).GetField("playerTransform", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            playerField?.SetValue(promptManager, playerTransform);
        }
    }
    
    /// <summary>
    /// 设置游戏UI
    /// </summary>
    private void SetupGameUI()
    {
        if (gameUI == null)
        {
            gameUI = FindObjectOfType<DanceGameUI>();
            if (gameUI == null)
            {
                GameObject uiGO = new GameObject("DanceGameUI");
                uiGO.transform.SetParent(transform);
                gameUI = uiGO.AddComponent<DanceGameUI>();
                
                // 创建默认UI布局
                gameUI.CreateDefaultUI();
                Debug.Log("创建了DanceGameUI");
            }
        }
    }
    
    /// <summary>
    /// 创建默认提示点预制体
    /// </summary>
    private void CreateDefaultPromptPrefab()
    {
        if (promptPointPrefab != null) return;
        
        // 创建提示点预制体
        GameObject prefabGO = new GameObject("PromptPoint");
        
        // 添加DancePromptPoint组件
        DancePromptPoint promptPoint = prefabGO.AddComponent<DancePromptPoint>();
        
        // 添加视觉组件
        PromptPointVisual visual = prefabGO.AddComponent<PromptPointVisual>();
        visual.CreateDefaultPromptPoint();
        
        // 设置为预制体引用
        promptPointPrefab = prefabGO;
        
        Debug.Log("创建了默认提示点预制体");
    }
    
    /// <summary>
    /// 连接所有组件
    /// </summary>
    private void ConnectComponents()
    {
        // 连接游戏控制器和提示点管理器
        if (gameController != null && promptManager != null)
        {
            var managerField = typeof(DanceGameController).GetField("promptManager", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            managerField?.SetValue(gameController, promptManager);
        }
        
        // 连接UI和游戏控制器
        if (gameUI != null && gameController != null)
        {
            var controllerField = typeof(DanceGameUI).GetField("gameController", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            controllerField?.SetValue(gameUI, gameController);
        }
        
        // 设置提示点预制体
        if (promptManager != null && promptPointPrefab != null)
        {
            var prefabField = typeof(DancePromptManager).GetField("promptPointPrefab", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            prefabField?.SetValue(promptManager, promptPointPrefab);
        }
        
        Debug.Log("组件连接完成");
    }
    
    /// <summary>
    /// 测试系统
    /// </summary>
    [ContextMenu("测试舞蹈系统")]
    public void TestSystem()
    {
        if (gameController == null)
        {
            Debug.LogError("游戏控制器未设置！");
            return;
        }
        
        Debug.Log("开始测试舞蹈系统...");
        gameController.TestShowPrompt();
    }
    
    /// <summary>
    /// 重置系统
    /// </summary>
    [ContextMenu("重置舞蹈系统")]
    public void ResetSystem()
    {
        if (gameController != null)
        {
            gameController.StopGame();
        }
        
        if (promptManager != null)
        {
            promptManager.HideAllPromptPoints();
        }
        
        Debug.Log("舞蹈系统已重置");
    }
    
    /// <summary>
    /// 验证系统设置
    /// </summary>
    [ContextMenu("验证系统设置")]
    public void ValidateSystem()
    {
        bool isValid = true;
        
        if (gameController == null)
        {
            Debug.LogError("缺少DanceGameController");
            isValid = false;
        }
        
        if (promptManager == null)
        {
            Debug.LogError("缺少DancePromptManager");
            isValid = false;
        }
        
        if (playerTransform == null)
        {
            Debug.LogError("缺少玩家Transform");
            isValid = false;
        }
        
        if (promptPointPrefab == null)
        {
            Debug.LogError("缺少提示点预制体");
            isValid = false;
        }
        
        if (isValid)
        {
            Debug.Log("✓ 舞蹈系统设置验证通过！");
        }
        else
        {
            Debug.LogError("✗ 舞蹈系统设置不完整，请检查缺少的组件");
        }
    }
    
    private void OnValidate()
    {
        // 在编辑器中自动查找玩家
        if (autoFindPlayer && playerTransform == null)
        {
            FindPlayerTransform();
        }
    }
}
