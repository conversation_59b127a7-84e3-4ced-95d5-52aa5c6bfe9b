using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using MGJointsDetect;

/// <summary>
/// 舞蹈游戏主控制器
/// 负责游戏流程控制、动作检测和评分
/// </summary>
public class DanceGameController : MonoBehaviour
{
    [Header("游戏设置")] [SerializeField] private float promptDuration = 3.0f; // 提示点显示时长
    [SerializeField] private float promptInterval = 1.0f; // 提示点间隔时间
    [SerializeField] private int maxPrompts = 10; // 最大提示点数量（仅在非音乐模式下使用）

    [SerializeField] private float musicDuration = 60f; // 音乐总时长（秒）
    [SerializeField] private float beatInterval = 0.5f; // 节拍间隔（秒）

    [Header("组件引用")] [SerializeField] private DancePromptManager promptManager;

    [Header("调试设置")] [SerializeField] private bool enableDebugLog = true;

    // 游戏状态
    private bool isGameActive = false;
    private int currentPromptCount = 0;
    private int correctCount = 0;
    private int totalCount = 0;

    // 音乐相关状态
    private float gameStartTime = 0f;
    private float musicStartTime = 0f;
    private bool isMusicPlaying = false;

    // 当前活跃的提示点信息
    private struct ActivePrompt
    {
        public int areaIndex;
        public bool isLeftHand;
        public float startTime;
        public bool isCompleted;
    }

    private List<ActivePrompt> activePrompts = new List<ActivePrompt>();

    // 事件
    public System.Action<int, int> OnScoreChanged; // (正确数, 总数)
    public System.Action OnGameStarted;
    public System.Action OnGameEnded;

    private void Awake()
    {
        if (promptManager == null)
        {
            promptManager = FindObjectOfType<DancePromptManager>();
        }
    }

    private void Start()
    {
        // 设置舞蹈检测模式
        JointsDetect.Instance.SetDanceGameType(DanceGameType.DetectAll);
        StartGame();
    }

    private void Update()
    {   // 获取舞蹈检测数据
        var detectResult = JointsDetect.Instance.GetDetectResultAndClear();
        // if (detectResult != null && detectResult.currentDanceModels.Count > 0)
        // {
        //     // 处理最新的检测数据
        //     var latestModel = detectResult.currentDanceModels[detectResult.currentDanceModels.Count - 1];
        //     OnDanceDetected(latestModel);
        // }
    }

    /// <summary>
    /// 开始游戏
    /// </summary>
    public void StartGame()
    {
        if (isGameActive)
        {
            Debug.LogWarning("游戏已经在进行中");
            return;
        }

        isGameActive = true;
        currentPromptCount = 0;
        correctCount = 0;
        totalCount = 0;
        activePrompts.Clear();

        gameStartTime = Time.time;

        OnGameStarted?.Invoke();

        // 根据模式开始游戏
        StartMusicMode();
    }

    /// <summary>
    /// 开始音乐模式
    /// </summary>
    private void StartMusicMode()
    {
        // 播放音乐
        SetTestMusicParameters();
        musicStartTime = Time.time;
        isMusicPlaying = true;
        if (enableDebugLog)
        {
            Debug.Log($"开始播放音乐，时长: {musicDuration:F1}秒");
        }
        // 开始生成音乐节拍提示点
        StartCoroutine(GenerateMusicPrompts());
    }
    

    /// <summary>
    /// 停止游戏
    /// </summary>
    public void StopGame()
    {
        if (!isGameActive) return;

        isGameActive = false;
        isMusicPlaying = false;
        StopAllCoroutines();
        // 隐藏所有提示点
        if (promptManager != null)
        {
            promptManager.HideAllPromptPoints();
        }

        activePrompts.Clear();

        OnGameEnded?.Invoke();

        if (enableDebugLog)
        {
            Debug.Log($"舞蹈游戏结束 - 得分: {correctCount}/{totalCount}");
        }
    }

    /// <summary>
    /// 生成音乐模式提示点协程
    /// </summary>
    private IEnumerator GenerateMusicPrompts()
    {
        float nextBeatTime = beatInterval;

        while (isGameActive && isMusicPlaying)
        {
            float currentMusicTime = Time.time - musicStartTime;

            // 检查音乐是否结束
            if (musicDuration > 0 && currentMusicTime >= musicDuration)
            {
                if (enableDebugLog)
                {
                    Debug.Log("音乐播放完毕，游戏结束");
                }

                break;
            }

            // 检查是否到了下一个节拍
            if (currentMusicTime >= nextBeatTime)
            {
                // 生成随机提示点
                int areaIndex = Random.Range(0, 9);
                bool isLeftHand = Random.Range(0, 2) == 0;

                // 显示提示点
                ShowPrompt(areaIndex, isLeftHand);

                currentPromptCount++;
                nextBeatTime += beatInterval;

                if (enableDebugLog)
                {
                    Debug.Log($"节拍 {currentPromptCount}: 区域{areaIndex}, 左手:{isLeftHand}, 时间:{currentMusicTime:F1}s");
                }
            }

            yield return null; // 等待下一帧
        }

        // 等待最后的提示点完成
        yield return new WaitForSeconds(promptDuration);

        // 游戏结束
        StopGame();
    }
    
    /// <summary>
    /// 显示提示点
    /// </summary>
    private void ShowPrompt(int areaIndex, bool isLeftHand)
    {
        if (promptManager == null) return;

        // 创建活跃提示点记录
        ActivePrompt prompt = new ActivePrompt
        {
            areaIndex = areaIndex,
            isLeftHand = isLeftHand,
            startTime = Time.time,
            isCompleted = false
        };

        activePrompts.Add(prompt);
        totalCount++;

        // 显示提示点
        promptManager.ShowPromptPoint(areaIndex, isLeftHand, promptDuration);

        // 设置超时处理
        StartCoroutine(HandlePromptTimeout(prompt));

        if (enableDebugLog)
        {
            Debug.Log($"显示提示点: 区域{areaIndex}, 左手:{isLeftHand}");
        }

        // 更新分数显示
        OnScoreChanged?.Invoke(correctCount, totalCount);
    }

    public void TestShowAllPrompts()
    {
        if (promptManager == null) return;
        for (int i = 0; i < 9; i++)
        {
            promptManager.ShowPromptPoint(i, false, 10f);
        }
    }

    /// <summary>
    /// 处理提示点超时
    /// </summary>
    private IEnumerator HandlePromptTimeout(ActivePrompt prompt)
    {
        yield return new WaitForSeconds(promptDuration);

        // 检查是否已完成
        for (int i = 0; i < activePrompts.Count; i++)
        {
            if (activePrompts[i].areaIndex == prompt.areaIndex &&
                activePrompts[i].startTime == prompt.startTime &&
                !activePrompts[i].isCompleted)
            {
                // 超时失败
                var updatedPrompt = activePrompts[i];
                updatedPrompt.isCompleted = true;
                activePrompts[i] = updatedPrompt;

                if (enableDebugLog)
                {
                    Debug.Log($"提示点超时: 区域{prompt.areaIndex}");
                }

                break;
            }
        }
    }

    /// <summary>
    /// 处理舞蹈检测事件
    /// </summary>
    private void OnDanceDetected(DanceDectectModel danceModel)
    {
        if (!isGameActive || activePrompts.Count == 0) return;

        // 检查是否匹配任何活跃的提示点
        for (int i = 0; i < activePrompts.Count; i++)
        {
            var prompt = activePrompts[i];
            if (prompt.isCompleted) continue;

            bool isMatch = false;

            // 检查左手匹配
            if (prompt.isLeftHand && danceModel.leftHandArea == prompt.areaIndex)
            {
                isMatch = true;
            }
            // 检查右手匹配
            else if (!prompt.isLeftHand && danceModel.rightHandArea == prompt.areaIndex)
            {
                isMatch = true;
            }

            if (isMatch)
            {
                // 匹配成功
                var updatedPrompt = prompt;
                updatedPrompt.isCompleted = true;
                activePrompts[i] = updatedPrompt;

                correctCount++;

                // 完成提示点
                if (promptManager != null)
                {
                    promptManager.CompletePromptPoint(prompt.areaIndex);
                }

                if (enableDebugLog)
                {
                    Debug.Log($"动作匹配成功: 区域{prompt.areaIndex}, 左手:{prompt.isLeftHand}");
                }

                // 更新分数显示
                OnScoreChanged?.Invoke(correctCount, totalCount);

                break; // 只匹配第一个符合条件的提示点
            }
        }
    }
    

    /// <summary>
    /// 设置音乐参数
    /// </summary>
    /// <param name="audioSource">音频源</param>
    /// <param name="duration">音乐时长（秒）</param>
    /// <param name="beatInterval">节拍间隔（秒）</param>
    public void SetMusicParameters( float duration, float beatInterval)
    {
        musicDuration = duration;
        this.beatInterval = beatInterval;

        if (enableDebugLog)
        {
            Debug.Log($"设置音乐参数 - 时长: {duration}s, 节拍间隔: {beatInterval}s");
        }
    }

    /// <summary>
    /// 设置节拍间隔
    /// </summary>
    /// <param name="interval">节拍间隔（秒）</param>
    public void SetBeatInterval(float interval)
    {
        this.beatInterval = interval;

        if (enableDebugLog)
        {
            Debug.Log($"设置节拍间隔: {interval}s");
        }
    }

    /// <summary>
    /// 设置音乐时长
    /// </summary>
    /// <param name="duration">音乐时长（秒）</param>
    public void SetMusicDuration(float duration)
    {
        this.musicDuration = duration;

        if (enableDebugLog)
        {
            Debug.Log($"设置音乐时长: {duration}s");
        }
    }

    /// <summary>
    /// 设置提示点显示时长
    /// </summary>
    /// <param name="duration">显示时长（秒）</param>
    public void SetPromptDuration(float duration)
    {
        this.promptDuration = duration;

        if (enableDebugLog)
        {
            Debug.Log($"设置提示点显示时长: {duration}s");
        }
    }
    

    /// <summary>
    /// 获取当前音乐播放时间
    /// </summary>
    /// <returns>音乐播放时间（秒）</returns>
    public float GetCurrentMusicTime()
    {
        if (!isMusicPlaying) return 0f;
        return Time.time - musicStartTime;
    }

    /// <summary>
    /// 获取音乐剩余时间
    /// </summary>
    /// <returns>剩余时间（秒）</returns>
    public float GetRemainingMusicTime()
    {
        if (!isMusicPlaying || musicDuration <= 0) return 0f;
        float remaining = musicDuration - GetCurrentMusicTime();
        return Mathf.Max(0f, remaining);
    }
    

    /// <summary>
    /// 检查音乐是否在播放
    /// </summary>
    /// <returns>音乐是否在播放</returns>
    public bool IsMusicPlaying()
    {
        return isMusicPlaying;
    }

    /// <summary>
    /// 测试方法：手动触发提示点
    /// </summary>
    [ContextMenu("测试显示提示点")]
    public void TestShowPrompt()
    {
        if (promptManager != null)
        {
            int randomArea = Random.Range(0, 9);
            bool randomHand = Random.Range(0, 2) == 0;
            ShowPrompt(randomArea, randomHand);
        }
    }

    /// <summary>
    /// 测试方法：设置测试音乐参数
    /// </summary>
    [ContextMenu("设置测试音乐参数")]
    public void SetTestMusicParameters()
    {
        SetMusicParameters(120f, 3f); // 30秒音乐，0.5秒节拍间隔
        Debug.Log("已设置测试音乐参数：60秒时长，0.5秒节拍间隔");
    }
}