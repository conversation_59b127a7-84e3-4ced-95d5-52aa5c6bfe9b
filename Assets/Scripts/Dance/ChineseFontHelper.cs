using UnityEngine;
using TMPro;

/// <summary>
/// 中文字体助手
/// 用于解决Unity中中文字体显示问题
/// </summary>
public static class ChineseFontHelper
{
    private static TMP_FontAsset _chineseFont;
    
    /// <summary>
    /// 获取中文字体
    /// </summary>
    public static TMP_FontAsset GetChineseFont()
    {
        if (_chineseFont == null)
        {
            LoadChineseFont();
        }
        return _chineseFont;
    }
    
    /// <summary>
    /// 加载中文字体
    /// </summary>
    private static void LoadChineseFont()
    {
        // 尝试加载项目中的中文字体
        _chineseFont = Resources.Load<TMP_FontAsset>("Fonts/fontawesome-webfont");
        
        if (_chineseFont == null)
        {
            // 尝试加载Unity内置字体
            _chineseFont = Resources.Load<TMP_FontAsset>("Fonts & Materials/LiberationSans SDF");
        }
        
        if (_chineseFont == null)
        {
            // 创建默认字体
            CreateDefaultChineseFont();
        }
        
        if (_chineseFont != null)
        {
            Debug.Log($"成功加载中文字体: {_chineseFont.name}");
        }
        else
        {
            Debug.LogWarning("无法加载中文字体，将使用系统默认字体");
        }
    }
    
    /// <summary>
    /// 创建默认中文字体
    /// </summary>
    private static void CreateDefaultChineseFont()
    {
        try
        {
            // 尝试使用系统字体创建TMP字体
            Font systemFont = Resources.GetBuiltinResource<Font>("LegacyRuntime.fontsettings");
            if (systemFont == null)
            {
                systemFont = Resources.GetBuiltinResource<Font>("Arial.ttf");
            }
            
            if (systemFont != null)
            {
                // 这里需要在编辑器中手动创建TMP字体资源
                Debug.Log("找到系统字体，但需要手动创建TMP字体资源");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"创建默认中文字体失败: {e.Message}");
        }
    }
    
    /// <summary>
    /// 设置文本组件的中文字体
    /// </summary>
    public static void SetChineseFont(TextMeshProUGUI textComponent)
    {
        if (textComponent == null) return;
        
        var font = GetChineseFont();
        if (font != null)
        {
            textComponent.font = font;
        }
    }
    
    /// <summary>
    /// 设置多个文本组件的中文字体
    /// </summary>
    public static void SetChineseFont(params TextMeshProUGUI[] textComponents)
    {
        var font = GetChineseFont();
        if (font == null) return;
        
        foreach (var textComponent in textComponents)
        {
            if (textComponent != null)
            {
                textComponent.font = font;
            }
        }
    }
    
    /// <summary>
    /// 检查是否有可用的中文字体
    /// </summary>
    public static bool HasChineseFont()
    {
        return GetChineseFont() != null;
    }
    
    /// <summary>
    /// 创建中文字体指南
    /// </summary>
    [System.Obsolete("此方法仅用于开发指导")]
    public static void CreateChineseFontGuide()
    {
        Debug.Log("=== 中文字体设置指南 ===");
        Debug.Log("1. 在Unity中打开 Window > TextMeshPro > Font Asset Creator");
        Debug.Log("2. 选择一个支持中文的字体文件（如SimHei.ttf、Microsoft YaHei等）");
        Debug.Log("3. 设置Character Set为Custom Characters");
        Debug.Log("4. 在Custom Character List中输入常用中文字符");
        Debug.Log("5. 点击Generate Font Atlas生成字体资源");
        Debug.Log("6. 保存为 Assets/Resources/Fonts/ChineseFont SDF.asset");
        Debug.Log("7. 重新运行游戏即可正常显示中文");
        Debug.Log("========================");
    }
}

/// <summary>
/// 中文字体设置组件
/// 可以添加到GameObject上自动设置中文字体
/// </summary>
public class ChineseFontSetter : MonoBehaviour
{
    [Header("自动设置")]
    [SerializeField] private bool setOnAwake = true;
    [SerializeField] private bool includeChildren = true;
    
    [Header("手动设置")]
    [SerializeField] private TextMeshProUGUI[] targetTexts;
    
    private void Awake()
    {
        if (setOnAwake)
        {
            SetChineseFonts();
        }
    }
    
    /// <summary>
    /// 设置中文字体
    /// </summary>
    [ContextMenu("设置中文字体")]
    public void SetChineseFonts()
    {
        // 设置手动指定的文本
        if (targetTexts != null && targetTexts.Length > 0)
        {
            ChineseFontHelper.SetChineseFont(targetTexts);
        }
        
        // 设置当前对象的文本
        var textComponent = GetComponent<TextMeshProUGUI>();
        if (textComponent != null)
        {
            ChineseFontHelper.SetChineseFont(textComponent);
        }
        
        // 设置子对象的文本
        if (includeChildren)
        {
            var childTexts = GetComponentsInChildren<TextMeshProUGUI>();
            ChineseFontHelper.SetChineseFont(childTexts);
        }
        
        Debug.Log($"已为 {gameObject.name} 设置中文字体");
    }
    
    /// <summary>
    /// 显示字体设置指南
    /// </summary>
    [ContextMenu("显示中文字体设置指南")]
    public void ShowFontGuide()
    {
        #pragma warning disable CS0618
        ChineseFontHelper.CreateChineseFontGuide();
        #pragma warning restore CS0618
    }
}
