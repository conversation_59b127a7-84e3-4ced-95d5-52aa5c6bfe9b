using System.Collections;
using UnityEngine;
using DG.Tweening;

/// <summary>
/// 舞蹈提示点控制器
/// 负责单个提示点的显示、动画和交互
/// </summary>
public class DancePromptPoint : MonoBehaviour
{
    [Header("提示点设置")]
    [SerializeField] private int areaIndex; // 对应0-8的区域索引
    [SerializeField] private bool isLeftHand; // 是否为左手提示点
    [SerializeField] private float showDuration = 2.0f; // 显示持续时间
    [SerializeField] private float pulseScale = 1.2f; // 脉动缩放比例
    [SerializeField] private float pulseDuration = 0.5f; // 脉动周期
    
    [Header("视觉组件")]
    [SerializeField] private GameObject visualObject; // 视觉表现对象
    [SerializeField] private Renderer pointRenderer; // 渲染器
    [SerializeField] private Color normalColor = Color.white; // 正常颜色
    [SerializeField] private Color successColor = Color.green; // 成功颜色
    [SerializeField] private Color failColor = Color.red; // 失败颜色
    
    private bool isActive = false;
    private bool isCompleted = false;
    private Tween pulseTween;
    private Tween showTween;
    
    public int AreaIndex => areaIndex;
    public bool IsLeftHand => isLeftHand;
    public bool IsActive => isActive;
    public bool IsCompleted => isCompleted;
    
    // 事件
    public System.Action<DancePromptPoint> OnPointCompleted;
    public System.Action<DancePromptPoint> OnPointFailed;
    
    private void Awake()
    {
        if (visualObject == null)
            visualObject = gameObject;
            
        if (pointRenderer == null)
            pointRenderer = GetComponent<Renderer>();
            
        // 初始状态隐藏
        SetVisible(false);
    }
    
    /// <summary>
    /// 显示提示点
    /// </summary>
    /// <param name="area">区域索引 0-8</param>
    /// <param name="leftHand">是否为左手</param>
    /// <param name="duration">显示时长</param>
    public void ShowPrompt(int area, bool leftHand, float duration = -1)
    {
        if (isActive) return;
        
        areaIndex = area;
        isLeftHand = leftHand;
        isCompleted = false;
        
        if (duration > 0)
            showDuration = duration;
            
        // 设置颜色
        SetColor(normalColor);
        
        // 显示动画
        SetVisible(true);
        isActive = true;
        
        // 开始脉动动画
        StartPulseAnimation();
        
        // 设置自动隐藏
        if (showDuration > 0)
        {
            showTween = DOVirtual.DelayedCall(showDuration, () => {
                if (!isCompleted)
                {
                    FailPrompt();
                }
            });
        }
    }
    
    /// <summary>
    /// 隐藏提示点
    /// </summary>
    public void HidePrompt()
    {
        if (!isActive) return;
        
        isActive = false;
        StopAllAnimations();
        SetVisible(false);
    }
    
    /// <summary>
    /// 成功完成提示点
    /// </summary>
    public void CompletePrompt()
    {
        if (!isActive || isCompleted) return;
        
        isCompleted = true;
        StopAllAnimations();
        
        // 成功动画
        SetColor(successColor);
        transform.DOScale(Vector3.one * 1.5f, 0.2f)
            .OnComplete(() => {
                transform.DOScale(Vector3.zero, 0.3f)
                    .OnComplete(() => {
                        HidePrompt();
                        OnPointCompleted?.Invoke(this);
                    });
            });
    }
    
    /// <summary>
    /// 失败提示点
    /// </summary>
    public void FailPrompt()
    {
        if (!isActive || isCompleted) return;
        
        isCompleted = true;
        StopAllAnimations();
        
        // 失败动画
        SetColor(failColor);
        transform.DOShakeScale(0.5f, 0.3f)
            .OnComplete(() => {
                transform.DOScale(Vector3.zero, 0.3f)
                    .OnComplete(() => {
                        HidePrompt();
                        OnPointFailed?.Invoke(this);
                    });
            });
    }
    
    /// <summary>
    /// 开始脉动动画
    /// </summary>
    private void StartPulseAnimation()
    {
        if (pulseTween != null)
            pulseTween.Kill();
            
        pulseTween = transform.DOScale(Vector3.one * pulseScale, pulseDuration)
            .SetEase(Ease.InOutSine)
            .SetLoops(-1, LoopType.Yoyo);
    }
    
    /// <summary>
    /// 停止所有动画
    /// </summary>
    private void StopAllAnimations()
    {
        pulseTween?.Kill();
        showTween?.Kill();
        transform.DOKill();
    }
    
    /// <summary>
    /// 设置可见性
    /// </summary>
    private void SetVisible(bool visible)
    {
        if (visualObject != null)
            visualObject.SetActive(visible);
    }
    
    /// <summary>
    /// 设置颜色
    /// </summary>
    private void SetColor(Color color)
    {
        if (pointRenderer != null)
            pointRenderer.material.color = color;
    }
    
    private void OnDestroy()
    {
        StopAllAnimations();
    }
    
    /// <summary>
    /// 设置提示点位置（由管理器调用）
    /// </summary>
    public void SetPosition(Vector3 position)
    {
        transform.position = position;
    }
    
    /// <summary>
    /// 重置提示点状态
    /// </summary>
    public void ResetPoint()
    {
        StopAllAnimations();
        isActive = false;
        isCompleted = false;
        transform.localScale = Vector3.one;
        SetVisible(false);
    }
}
