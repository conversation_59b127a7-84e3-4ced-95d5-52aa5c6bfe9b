using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 舞蹈提示点管理器
/// 负责管理玩家周围的所有提示点位置和显示
/// </summary>
public class DancePromptManager : MonoBehaviour
{
    [Header("玩家设置")]
    [SerializeField] private Transform playerTransform; // 玩家Transform
    [SerializeField] private float promptDistance = 2.0f; // 提示点距离玩家的距离
    [SerializeField] private float promptHeight = 1.5f; // 提示点相对玩家的高度
    
    [Header("提示点预制体")]
    [SerializeField] private GameObject promptPointPrefab; // 提示点预制体
    
    [Header("区域位置设置")]
    [SerializeField] private Vector3[] areaOffsets = new Vector3[9]; // 9个区域的相对位置偏移
    
    private DancePromptPoint[] promptPoints = new DancePromptPoint[9]; // 9个提示点
    private bool isInitialized = false;
    
    // 区域位置映射 (相对于玩家的偏移)
    private readonly Vector3[] defaultAreaOffsets = new Vector3[]
    {
        new Vector3(-1.5f, 1.0f, 1.5f),   // 0: 左上
        new Vector3(0f, 1.0f, 1.5f),      // 1: 中上  
        new Vector3(1.5f, 1.0f, 1.5f),    // 2: 右上
        new Vector3(-1.5f, 0f, 0f),       // 3: 中左
        new Vector3(0f, 0f, 0f),          // 4: 中中
        new Vector3(1.5f, 0f, 0f),        // 5: 中右
        new Vector3(-1.5f, -1.0f, -1.5f), // 6: 左下
        new Vector3(0f, -1.0f, -1.5f),    // 7: 中下
        new Vector3(1.5f, -1.0f, -1.5f)   // 8: 右下
    };
    
    private void Awake()
    {
        InitializeAreaOffsets();
    }
    
    private void Start()
    {
        if (playerTransform == null)
        {
            // 尝试自动找到玩家
            FindPlayerTransform();
        }
        
        InitializePromptPoints();
    }
    
    /// <summary>
    /// 初始化区域偏移
    /// </summary>
    private void InitializeAreaOffsets()
    {
        // 如果没有设置自定义偏移，使用默认值
        if (areaOffsets == null || areaOffsets.Length != 9)
        {
            areaOffsets = new Vector3[9];
        }
        
        for (int i = 0; i < 9; i++)
        {
            if (areaOffsets[i] == Vector3.zero)
            {
                areaOffsets[i] = defaultAreaOffsets[i];
            }
        }
    }
    
    /// <summary>
    /// 查找玩家Transform
    /// </summary>
    private void FindPlayerTransform()
    {
        // 尝试通过Players节点找到玩家
        GameObject playersGO = GameObject.Find("Players");
        if (playersGO != null)
        {
            // 假设第二个子对象是玩家 (SK_GamerGirl_03 Pink Variant)
            if (playersGO.transform.childCount > 1)
            {
                playerTransform = playersGO.transform.GetChild(1);
                Debug.Log($"找到玩家: {playerTransform.name}");
            }
        }
        
        if (playerTransform == null)
        {
            Debug.LogWarning("未找到玩家Transform，请手动设置playerTransform");
        }
    }
    
    /// <summary>
    /// 初始化提示点
    /// </summary>
    private void InitializePromptPoints()
    {
        if (playerTransform == null || promptPointPrefab == null)
        {
            Debug.LogError("缺少必要组件：playerTransform 或 promptPointPrefab");
            return;
        }
        
        // 创建9个提示点
        for (int i = 0; i < 9; i++)
        {
            GameObject pointGO = Instantiate(promptPointPrefab, transform);
            pointGO.name = $"PromptPoint_{i}";
            
            DancePromptPoint point = pointGO.GetComponent<DancePromptPoint>();
            if (point == null)
            {
                point = pointGO.AddComponent<DancePromptPoint>();
            }
            
            promptPoints[i] = point;
            
            // 设置初始位置
            UpdatePromptPointPosition(i);
            
            // 绑定事件
            point.OnPointCompleted += OnPromptPointCompleted;
            point.OnPointFailed += OnPromptPointFailed;
        }
        
        isInitialized = true;
        Debug.Log("舞蹈提示点系统初始化完成");
    }
    
    /// <summary>
    /// 更新提示点位置
    /// </summary>
    private void UpdatePromptPointPosition(int areaIndex)
    {
        if (areaIndex < 0 || areaIndex >= 9 || promptPoints[areaIndex] == null)
            return;
            
        Vector3 worldPosition = GetWorldPositionForArea(areaIndex);
        promptPoints[areaIndex].SetPosition(worldPosition);
    }
    
    /// <summary>
    /// 获取指定区域的世界坐标位置
    /// </summary>
    private Vector3 GetWorldPositionForArea(int areaIndex)
    {
        if (playerTransform == null || areaIndex < 0 || areaIndex >= 9)
            return Vector3.zero;
            
        // 基于玩家位置和朝向计算世界坐标
        Vector3 localOffset = areaOffsets[areaIndex];
        
        // 考虑玩家的朝向
        Vector3 forward = playerTransform.forward;
        Vector3 right = playerTransform.right;
        Vector3 up = playerTransform.up;
        
        Vector3 worldOffset = right * localOffset.x + up * localOffset.y + forward * localOffset.z;
        
        return playerTransform.position + worldOffset + Vector3.up * promptHeight;
    }
    
    /// <summary>
    /// 显示指定区域的提示点
    /// </summary>
    /// <param name="areaIndex">区域索引 0-8</param>
    /// <param name="isLeftHand">是否为左手</param>
    /// <param name="duration">显示时长</param>
    public void ShowPromptPoint(int areaIndex, bool isLeftHand, float duration = 2.0f)
    {
        if (!isInitialized || areaIndex < 0 || areaIndex >= 9)
        {
            Debug.LogWarning($"无效的区域索引: {areaIndex}");
            return;
        }
        
        // 更新位置
        UpdatePromptPointPosition(areaIndex);
        
        // 显示提示点
        promptPoints[areaIndex].ShowPrompt(areaIndex, isLeftHand, duration);
        
        Debug.Log($"显示提示点 - 区域: {areaIndex}, 左手: {isLeftHand}, 时长: {duration}s");
    }
    
    /// <summary>
    /// 隐藏指定区域的提示点
    /// </summary>
    public void HidePromptPoint(int areaIndex)
    {
        if (!isInitialized || areaIndex < 0 || areaIndex >= 9)
            return;
            
        promptPoints[areaIndex].HidePrompt();
    }
    
    /// <summary>
    /// 隐藏所有提示点
    /// </summary>
    public void HideAllPromptPoints()
    {
        if (!isInitialized) return;
        
        for (int i = 0; i < 9; i++)
        {
            promptPoints[i].HidePrompt();
        }
    }
    
    /// <summary>
    /// 完成指定区域的提示点
    /// </summary>
    public void CompletePromptPoint(int areaIndex)
    {
        if (!isInitialized || areaIndex < 0 || areaIndex >= 9)
            return;
            
        promptPoints[areaIndex].CompletePrompt();
    }
    
    /// <summary>
    /// 获取指定区域的提示点
    /// </summary>
    public DancePromptPoint GetPromptPoint(int areaIndex)
    {
        if (!isInitialized || areaIndex < 0 || areaIndex >= 9)
            return null;
            
        return promptPoints[areaIndex];
    }
    
    /// <summary>
    /// 提示点完成事件处理
    /// </summary>
    private void OnPromptPointCompleted(DancePromptPoint point)
    {
        Debug.Log($"提示点完成: 区域{point.AreaIndex}, 左手:{point.IsLeftHand}");
        // 这里可以触发游戏事件
    }
    
    /// <summary>
    /// 提示点失败事件处理
    /// </summary>
    private void OnPromptPointFailed(DancePromptPoint point)
    {
        Debug.Log($"提示点失败: 区域{point.AreaIndex}, 左手:{point.IsLeftHand}");
        // 这里可以触发游戏事件
    }
    
    private void Update()
    {
        // 实时更新提示点位置（如果玩家在移动）
        if (isInitialized && playerTransform != null)
        {
            for (int i = 0; i < 9; i++)
            {
                if (promptPoints[i].IsActive)
                {
                    UpdatePromptPointPosition(i);
                }
            }
        }
    }
    
    /// <summary>
    /// 设置提示点距离
    /// </summary>
    public void SetPromptDistance(float distance)
    {
        promptDistance = distance;
        // 重新计算偏移
        for (int i = 0; i < 9; i++)
        {
            areaOffsets[i] = defaultAreaOffsets[i] * (distance / 2.0f);
        }
    }
}
