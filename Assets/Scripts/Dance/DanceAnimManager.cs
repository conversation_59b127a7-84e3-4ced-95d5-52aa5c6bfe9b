using System;
using UnityEngine;

public class DanceAnimManager : MonoBehaviour
{
    public Animator animator;
    private int idleLayer = -1;

    
    void Awake()
    {
        animator = GetComponent<Animator>();
    }

    private void Start()
    {
        idleLayer = animator.GetLayerIndex("Fail");
    }

    public void SwitchToIdle()
    {
        animator.SetLayerWeight(idleLayer, 1);
    }
    
    public void SwitchToDance()
    {
        animator.SetLayerWeight(idleLayer, 0);
    }
}